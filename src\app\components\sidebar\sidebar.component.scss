@import "../../../assets/styles/_variables";

.sidebar {
  width: 316px;
  height: 100vh;
  background-color: $neutral-light-grey;
  display: flex;
  padding: 80px 24px 24px;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  overflow: auto;
  transition: width 0.3s ease;
}

.sidebar.collapsed {
  width: 80px;
  padding: 80px 16px 24px;
}

.button-container {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.route-button {
  width: 100%;
  padding: 8px;
  display: flex;
  align-items: center;
  cursor: pointer;
  margin-bottom: 12px;
  border-radius: 8px;
}

.route-button:hover {
  background-color: $primary-blue-03;
}

.route-button-active {
  background-color: $primary-blue-02;
  border-radius: 8px;
}

.icon-box {
  width: 40px;
  height: 40px;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 100%;
  margin-right: 24px;
}

.i-paper-clip,
.i-document {
  width: 24px;
  height: 24px;
  min-height: 24px;
  min-width: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 13px;
  margin-right: 13px;
}

.i-sidebar-open,
.i-sidebar-close {
  width: 24px;
  height: 24px;
  min-height: 24px;
  min-width: 24px;
  display: block;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.hide {
  display: none;
}

.route-button-collapsed {
  justify-content: center;
  padding: 8px 4px;
}

.icon-box-collapsed {
  margin-right: 0;
}

.button-container-collapsed {
  align-items: center;
}

.sidebar-toggle-btn {
  background-color: $neutral-light-grey !important;
  border-color: $neutral-light-grey !important;
  position: fixed;
  left: 296px;
  top: 50%;
  transform: translateY(-50%);
  width: 40px !important;
  height: 40px !important;
  border-radius: 50% !important;
  z-index: 1000;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px #eff0f7;
}

.sidebar.collapsed + .sidebar-toggle-btn {
  left: 60px;
}

::ng-deep .p-button-text.p-button-secondary:not(:disabled):hover {
  background: transparent !important;
}